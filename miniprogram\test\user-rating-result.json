{"request": {"url": "http://localhost:5000/api/v1/users/comparison-ratings", "method": "POST", "headers": {"Authorization": "Bearer your-test-token-here"}, "body": {"comparisonCacheId": "688615e268e9124c4d24a796", "rating": 4.5}}, "response": {"statusCode": 401, "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "access-control-allow-origin": "*", "content-type": "application/json; charset=utf-8", "content-length": "82", "etag": "W/\"52-1mWopUCpMa9oHoHJqYwjRILcdC4\"", "date": "Sun, 27 Jul 2025 12:53:53 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "data": {"success": false, "code": 401, "message": "无效的令牌", "timestamp": 1753620833065}}, "timestamp": "2025-07-27T12:53:53.071Z"}