const ComparisonRating = require('../../models/ComparisonRating');
const ProductComparisonV4Cache = require('../../models/ProductComparisonV4Cache');
const User = require('../../models/User');
const mongoose = require('mongoose');

/**
 * 用户对AI产品对比结果评分服务
 * 简化版本：每个用户只能评分一次，不允许修改
 */

/**
 * 用户对AI对比结果进行评分
 * @param {String} userId 用户ID
 * @param {String} comparisonCacheId 产品对比缓存ID
 * @param {Number} rating 评分 (0-5星，支持0.5增量)
 * @returns {Promise<Object>} 评分结果
 */
const rateComparison = async (userId, comparisonCacheId, rating) => {
  try {
    // 1. 验证输入参数
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return {
        success: false,
        error: '无效的用户ID',
        data: null
      };
    }
    
    if (!mongoose.Types.ObjectId.isValid(comparisonCacheId)) {
      return {
        success: false,
        error: '无效的对比结果ID',
        data: null
      };
    }
    
    // 验证评分范围和格式
    if (typeof rating !== 'number' || rating < 0 || rating > 5 || rating % 0.5 !== 0) {
      return {
        success: false,
        error: '评分必须是0-5之间的数字，支持0.5增量',
        data: null
      };
    }
    
    // 2. 验证用户是否存在且活跃
    const user = await User.findById(userId).select('isActive');
    if (!user) {
      return {
        success: false,
        error: '用户不存在',
        data: null
      };
    }
    
    if (!user.isActive) {
      return {
        success: false,
        error: '账户已被禁用',
        data: null
      };
    }
    
    // 3. 验证对比结果是否存在
    const comparisonCache = await ProductComparisonV4Cache.findById(comparisonCacheId)
      .select('productNames aiAnalysis.productCategory');
    
    if (!comparisonCache) {
      return {
        success: false,
        error: '对比结果不存在',
        data: null
      };
    }
    
    // 4. 检查用户是否已经评分过（每个用户只能评分一次）
    const existingRating = await ComparisonRating.findOne({
      comparisonCacheId: comparisonCacheId,
      userId: userId
    });
    
    if (existingRating) {
      return {
        success: false,
        error: '您已经对此对比结果评分过了，每个用户只能评分一次',
        data: null
      };
    }
    
    // 5. 创建新评分
    const ratingRecord = new ComparisonRating({
      comparisonCacheId: comparisonCacheId,
      userId: userId,
      rating: rating,
      productNames: comparisonCache.productNames,
      productCategory: comparisonCache.aiAnalysis.productCategory
    });
    
    const savedRating = await ratingRecord.save();
    
    // 6. 获取更新后的平均评分
    const averageRatingData = await ComparisonRating.getAverageRating(comparisonCacheId);
    
    // 7. 同步更新ProductComparisonV4Cache中的评分统计信息
    try {
      await comparisonCache.updateRatingStats(
        averageRatingData.averageRating,
        averageRatingData.totalRatings
      );
    } catch (updateError) {
      console.warn('更新缓存评分统计失败:', updateError.message);
      // 不影响主要功能，继续执行
    }
    
    return {
      success: true,
      data: {
        ratingId: savedRating._id,
        userRating: rating,
        createdAt: savedRating.createdAt,
        message: '评分成功',
        // 更新后的评分统计信息
        updatedRatingStats: {
          averageRating: averageRatingData.averageRating,
          totalRatings: averageRatingData.totalRatings,
          lastRatingUpdate: new Date()
        }
      }
    };
    
  } catch (error) {
    console.error('用户评分失败:', error);
    
    // 处理唯一索引冲突错误
    if (error.code === 11000) {
      return {
        success: false,
        error: '您已经对此对比结果评分过了，每个用户只能评分一次',
        data: null
      };
    }
    
    return {
      success: false,
      error: `评分失败: ${error.message}`,
      data: null
    };
  }
};

module.exports = {
  rateComparison
};