<!--components/rate-score/rate-score.wxml-->
<wxs module="utils">
  // 获取星星显示状态
  var getStarClass = function(starValue, rating) {
    if (rating >= starValue) {
      return 'star-full';
    } else if (rating >= starValue - 0.5) {
      return 'star-half';
    } else {
      return 'star-empty';
    }
  };

  // 格式化评分显示
  var formatRating = function(rating) {
    if (!rating || rating === 0) {
      return '0.0';
    }
    return rating.toFixed(1);
  };

  // 格式化评分数量显示
  var formatRatingCount = function(count) {
    if (!count || count === 0) {
      return '暂无评分';
    }
    return count + '人评分';
  };

  module.exports = {
    getStarClass: getStarClass,
    formatRating: formatRating,
    formatRatingCount: formatRatingCount
  };
</wxs>

<view class="rate-score-container size-{{size}}">
  
  <!-- 评分统计显示 -->
  <view wx:if="{{showStats}}" class="rating-stats">
    <view class="average-rating">
      <view class="rating-number">{{utils.formatRating(averageRating)}}</view>
      <view class="rating-stars">
        <view 
          class="star {{utils.getStarClass(item, averageRating)}}"
          wx:for="{{stars}}"
          wx:key="*this"
        >
          <text class="star-icon">★</text>
        </view>
      </view>
    </view>
    <view class="rating-count">{{utils.formatRatingCount(totalRatings)}}</view>
  </view>

  <!-- 用户评分区域 -->
  <view class="user-rating-section">
    <view class="rating-title">
      <text wx:if="{{!hasRated && !disabled}}">为这个AI分析结果评分</text>
      <text wx:elif="{{hasRated}}">您的评分：{{utils.formatRating(userRating)}}</text>
      <text wx:else>AI分析结果评分</text>
    </view>
    
    <view class="rating-stars-interactive">
      <view 
        class="star-interactive {{utils.getStarClass(item * 0.5, userRating)}} {{disabled || hasRated ? 'disabled' : ''}}"
        wx:for="{{halfStars}}"
        wx:key="*this"
        data-rating="{{item}}"
        bindtap="onStarTap"
      >
        <text class="star-icon">★</text>
      </view>
    </view>

    <!-- 评分提示 -->
    <view class="rating-hint">
      <text wx:if="{{submitting}}">正在提交评分...</text>
      <text wx:elif="{{hasRated}}">感谢您的评分！</text>
      <text wx:elif="{{disabled}}">请登录后评分</text>
      <text wx:else>点击星星进行评分（支持半星）</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{submitting}}" class="loading-overlay">
    <view class="loading-spinner"></view>
  </view>

</view>
