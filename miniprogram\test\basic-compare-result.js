/**
 * 简单的基础产品对比测试脚本
 * 对比产品：华为 Mate 70 Pro 和 苹果iPhone 16 Pro
 * 使用方法：node simple-test.js
 */

const fs = require('fs');
const path = require('path');

// 配置后端服务器地址
const BASE_URL = 'http://localhost:5000/api/v1';

/**
 * 保存数据到JSON文件
 */
function saveToJsonFile(data) {
  const filename = 'basic-compare-result.json';
  const filePath = path.join(__dirname, filename);
  const jsonData = JSON.stringify(data, null, 2);

  try {
    fs.writeFileSync(filePath, jsonData, 'utf8');
    console.log(`✅ 数据已保存到: ${filePath}`);
  } catch (error) {
    console.error('❌ 保存文件失败:', error);
  }
}

/**
 * 发送HTTP请求 (Node.js环境)
 */
function sendRequest(url, data) {
  return new Promise((resolve, reject) => {
    const https = require('https');
    const http = require('http');
    const urlObj = new URL(url);

    const postData = JSON.stringify(data);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const protocol = urlObj.protocol === 'https:' ? https : http;

    const req = protocol.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve(jsonData);
        } catch (error) {
          reject(new Error('响应数据解析失败: ' + error.message));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

/**
 * 执行基础对比测试
 */
async function testBasicCompare() {
  console.log('🧪 开始测试基础产品对比接口...');
  console.log('📱 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro');

  const requestData = {
    productNames: ['华为 Mate 70 Pro', '苹果iPhone 16 Pro']
  };
  
  const apiUrl = `${BASE_URL}/products/compare-basic`;

  try {
    console.log('📡 正在发送请求...');
    const response = await sendRequest(apiUrl, requestData);
    
    console.log('✅ 请求成功!');
    console.log('📊 响应数据:', response);
    
    // 保存完整响应数据
    saveToJsonFile(response);
    
    console.log('🎉 测试完成，数据已保存!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 运行测试
 */
function runTest() {
  testBasicCompare();
}

// 如果直接运行此脚本文件，自动执行测试
if (require.main === module) {
  console.log('� 启动基础产品对比测试...');
  runTest();
}

// 导出函数
module.exports = {
  testBasicCompare,
  runTest
};
