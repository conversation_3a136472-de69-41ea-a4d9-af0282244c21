/* components/rate-score/rate-score.wxss */

.rate-score-container {
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}

/* 尺寸变体 */
.rate-score-container.size-small {
  padding: 20rpx;
  margin: 16rpx 0;
}

.rate-score-container.size-large {
  padding: 40rpx;
  margin: 30rpx 0;
}

/* 评分统计显示 */
.rating-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.average-rating {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rating-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b35;
  line-height: 1;
}

.size-small .rating-number {
  font-size: 36rpx;
}

.size-large .rating-number {
  font-size: 56rpx;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.rating-count {
  font-size: 26rpx;
  color: #666;
  text-align: right;
}

.size-small .rating-count {
  font-size: 24rpx;
}

/* 用户评分区域 */
.user-rating-section {
  text-align: center;
}

.rating-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.size-small .rating-title {
  font-size: 26rpx;
  margin-bottom: 16rpx;
}

.size-large .rating-title {
  font-size: 32rpx;
  margin-bottom: 24rpx;
}

/* 交互式星星 */
.rating-stars-interactive {
  display: flex;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.size-small .rating-stars-interactive {
  gap: 6rpx;
  margin-bottom: 16rpx;
}

.size-large .rating-stars-interactive {
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.star-interactive {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  transform-origin: center;
}

.star-interactive:active {
  transform: scale(1.2);
}

.star-interactive.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.star-interactive.disabled:active {
  transform: none;
}

/* 星星图标 */
.star {
  position: relative;
  display: inline-block;
}

.star-icon {
  font-size: 32rpx;
  color: #ddd;
  transition: color 0.2s ease;
}

.size-small .star-icon {
  font-size: 28rpx;
}

.size-large .star-icon {
  font-size: 40rpx;
}

/* 星星状态 */
.star-full .star-icon {
  color: #ff6b35;
}

.star-half .star-icon {
  background: linear-gradient(90deg, #ff6b35 50%, #ddd 50%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #ff6b35;
}

.star-empty .star-icon {
  color: #ddd;
}

/* 交互式星星悬停效果 */
.star-interactive:not(.disabled):hover .star-icon,
.star-interactive:not(.disabled):active .star-icon {
  color: #ff8c69;
  text-shadow: 0 0 8rpx rgba(255, 107, 53, 0.3);
}

/* 评分提示 */
.rating-hint {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.size-small .rating-hint {
  font-size: 22rpx;
}

.size-large .rating-hint {
  font-size: 26rpx;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  backdrop-filter: blur(4rpx);
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .rating-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
  
  .rating-count {
    text-align: left;
  }
  
  .rating-stars-interactive {
    gap: 6rpx;
  }
  
  .star-icon {
    font-size: 28rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .rate-score-container {
    background: #1a1a1a;
    border-color: #333;
    color: #fff;
  }
  
  .rating-title {
    color: #fff;
  }
  
  .rating-count {
    color: #999;
  }
  
  .rating-hint {
    color: #666;
  }
  
  .star-empty .star-icon {
    color: #444;
  }
}
